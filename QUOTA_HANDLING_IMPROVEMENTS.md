# Quota Handling Improvements for Gemini API

## Overview

This document describes the improvements made to handle Google Gemini API quota errors more effectively. The enhancements provide better error detection, intelligent retry delays, and detailed quota information extraction.

## Problem Analysis

### Original Error Message
```
ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. 
[violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_value: 15
}
, retry_delay {
  seconds: 46
}
]
```

### Key Information Available
1. **Error Type**: ResourceExhausted with HTTP 429 status
2. **Retry Delay**: API provides exact seconds to wait (46s in example)
3. **Quota Details**: Metric, ID, value (15 requests/minute)
4. **Model Information**: Specific model that hit the limit
5. **Quota Type**: Free tier vs paid tier identification

## Improvements Implemented

### 1. Enhanced QuotaExceededException Class

**File**: `src/providers/google_provider.py`

```python
class QuotaExceededException(Exception):
    def __init__(self, message: str, retry_delay: Optional[int] = None, quota_info: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.retry_delay = retry_delay  # API-suggested delay in seconds
        self.quota_info = quota_info or {}  # Detailed quota information
```

**Benefits**:
- Stores API-suggested retry delay
- Preserves detailed quota information
- Enables intelligent retry logic

### 2. Accurate Error Parsing

**Method**: `_parse_quota_error()`

```python
def _parse_quota_error(self, error: Exception) -> Optional[Dict[str, Any]]:
    """Parse quota error information from Google API error."""
```

**Features**:
- **Precise Detection**: Looks for "ResourceExhausted: 429" specifically
- **Regex Extraction**: Uses regex patterns to extract structured data
- **Multiple Fields**: Extracts retry_delay, quota_metric, quota_id, quota_value, model
- **Fallback Safe**: Returns None for non-quota errors

**Extracted Information**:
- `retry_delay`: Exact seconds to wait (from API)
- `quota_metric`: Full quota metric name
- `quota_id`: Quota identifier (e.g., "GenerateRequestsPerMinutePerProjectPerModel-FreeTier")
- `quota_value`: Limit value (e.g., 15 requests/minute)
- `model`: Specific model that hit the limit

### 3. Intelligent Retry Logic

**File**: `src/approaches/single_frontier.py`
**Method**: `_generate_with_retry()`

**Improvements**:

#### API-Suggested Delays
```python
if qe.retry_delay:
    # Add a small buffer (10% + 1 second) to the API-suggested delay
    delay = qe.retry_delay + (qe.retry_delay * 0.1) + 1
```

#### Detailed Logging
```python
print(f"⚠️  Quota exceeded on attempt {attempt + 1}/{max_retries + 1}. "
      f"API suggests {qe.retry_delay}s, waiting {delay:.1f}s...")
print(f"   📊 Quota details: {quota_metric} (limit: {quota_value}/min) for model: {model}")
```

#### Fallback Strategy
- Uses API delay when available
- Falls back to exponential backoff when no delay provided
- Maintains compatibility with other providers

## Usage Examples

### Basic Usage
The improvements are transparent to existing code. Your current API calls will automatically benefit from:
- More accurate quota error detection
- Intelligent retry delays based on API suggestions
- Detailed logging of quota information

### Error Information Access
```python
try:
    result = approach.predict_single(loan_data)
except QuotaExceededException as e:
    print(f"Retry delay: {e.retry_delay} seconds")
    print(f"Quota info: {e.quota_info}")
```

## Testing

Run the test script to verify improvements:
```bash
python test_quota_parsing.py
```

**Expected Output**:
```
✅ Is quota error: True
⏱️  Retry delay: 46 seconds
📈 Quota metric: generativelanguage.googleapis.com/generate_content_free_tier_requests
🆔 Quota ID: GenerateRequestsPerMinutePerProjectPerModel-FreeTier
📊 Quota value: 15
🤖 Model: gemini-2.0-flash
```

## Benefits Summary

### 1. More Accurate Detection
- **Before**: String matching on error message
- **After**: Precise HTTP 429 + ResourceExhausted detection

### 2. Intelligent Wait Times
- **Before**: Fixed exponential backoff (5, 7, 9, 13 seconds)
- **After**: API-suggested delay + buffer (e.g., 46s + 10% + 1s = 51.6s)

### 3. Better User Experience
- **Before**: Generic "quota error" messages
- **After**: Detailed quota information and precise wait times

### 4. Reduced API Waste
- **Before**: May retry too early, wasting API calls
- **After**: Waits exactly as long as API suggests

### 5. Enhanced Debugging
- **Before**: Limited error information
- **After**: Full quota details for troubleshooting

## Error Types Handled

### 1. Rate Limiting (Primary)
- **Quota ID**: `GenerateRequestsPerMinutePerProjectPerModel-FreeTier`
- **Limit**: 15 requests/minute for free tier
- **Handling**: Wait for API-suggested delay

### 2. Token Limits (Future)
- **Detection**: Would show different quota_metric
- **Handling**: Same intelligent retry logic

### 3. Billing Issues (Future)
- **Detection**: Different error patterns
- **Handling**: Graceful failure with clear messaging

## Configuration

No configuration changes required. The improvements work with your existing setup:

```yaml
# config.yaml - no changes needed
models:
  frontier:
    provider: "google"
    model_name: "gemini-2.0-flash"
    temperature: 0.1
    max_tokens: 2000
```

## Monitoring and Logging

The enhanced system provides detailed logging:
```
⚠️  Quota exceeded on attempt 1/6. API suggests 46s, waiting 51.6s...
   📊 Quota details: generativelanguage.googleapis.com/generate_content_free_tier_requests (limit: 15/min) for model: gemini-2.0-flash
```

This helps you:
- Understand quota usage patterns
- Optimize request timing
- Plan for quota upgrades
- Debug API issues

## Next Steps

1. **Monitor Performance**: Watch for reduced quota errors
2. **Analyze Patterns**: Use logging to understand usage patterns
3. **Consider Upgrades**: If hitting limits frequently, consider paid tier
4. **Extend to Other Providers**: Apply similar patterns to OpenAI, Anthropic, etc.
