#!/usr/bin/env python3
"""
Simple test script to demonstrate quota error parsing.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.providers.base import LLMConfig
from src.providers.google_provider import GoogleProvider


def test_quota_parsing():
    """Test quota error parsing functionality."""
    print("🧪 Testing Quota Error Parsing")
    print("=" * 50)
    
    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )
    
    # Create the provider
    provider = GoogleProvider(config)
    
    # Create a mock error message like the one you provided
    error_message = """ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]."""
    
    mock_error = Exception(error_message)
    
    # Test parsing the quota error
    quota_info = provider._parse_quota_error(mock_error)
    
    print("📊 Parsed quota information:")
    if quota_info:
        print(f"   ✅ Is quota error: {quota_info['is_quota_error']}")
        print(f"   ⏱️  Retry delay: {quota_info['retry_delay']} seconds")
        print(f"   📈 Quota metric: {quota_info['quota_metric']}")
        print(f"   🆔 Quota ID: {quota_info['quota_id']}")
        print(f"   📊 Quota value: {quota_info['quota_value']}")
        print(f"   🤖 Model: {quota_info['model']}")
        
        # Test what information we can extract
        print("\n🔍 Analysis:")
        print(f"   • You hit the rate limit for {quota_info['model']}")
        print(f"   • Your free tier allows {quota_info['quota_value']} requests per minute")
        print(f"   • The API suggests waiting {quota_info['retry_delay']} seconds")
        print(f"   • This is a '{quota_info['quota_id']}' quota")
        
        # Calculate recommended wait time
        api_delay = quota_info['retry_delay']
        recommended_delay = api_delay + (api_delay * 0.1) + 1  # Add 10% buffer + 1 second
        print(f"   • Recommended wait time: {recommended_delay:.1f} seconds (API: {api_delay}s + buffer)")
        
        return True
    else:
        print("   ❌ Failed to parse quota error!")
        return False


def test_non_quota_error():
    """Test that non-quota errors are not detected as quota errors."""
    print("\n🧪 Testing Non-Quota Error Detection")
    print("=" * 50)
    
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )
    
    provider = GoogleProvider(config)
    
    # Test with a non-quota error
    non_quota_error = Exception("Invalid API key provided")
    quota_info = provider._parse_quota_error(non_quota_error)
    
    if quota_info is None:
        print("   ✅ Correctly identified as non-quota error")
        return True
    else:
        print("   ❌ Incorrectly identified as quota error")
        return False


if __name__ == "__main__":
    print("🏦 Quota Error Parsing Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Quota error parsing
    if test_quota_parsing():
        tests_passed += 1
    
    # Test 2: Non-quota error detection
    if test_non_quota_error():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed!")
        print("\n🎉 Key Improvements:")
        print("   1. Accurate detection of ResourceExhausted 429 errors")
        print("   2. Extraction of retry_delay from API response")
        print("   3. Parsing of quota details (metric, value, model)")
        print("   4. Intelligent wait time calculation with buffer")
        print("   5. Detailed logging of quota information")
    else:
        print("❌ Some tests failed!")
