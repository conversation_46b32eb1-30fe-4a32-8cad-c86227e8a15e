#!/usr/bin/env python3
"""
Test script to verify structured quota error parsing and same-sample retry logic.
"""

from src.providers.google_provider import GoogleProvider, QuotaExceededException
from src.approaches.single_frontier import SingleFrontierApproach
from src.providers.base import LLMConfig
from src.models.loan_data import LoanData, HomeOwnership, VerificationStatus, LoanPurpose
from src.models.llm_response import LoanDecision
import sys
import os
import time
from unittest.mock import Mock, patch

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_structured_quota_parsing():
    """Test that structured quota error parsing works without regex."""
    print("🧪 Testing Structured Quota Error Parsing (No Regex)")
    print("=" * 60)

    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )

    # Create the provider
    provider = GoogleProvider(config)

    # Your original error message
    error_message = """ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]."""

    mock_error = Exception(error_message)

    # Test parsing the quota error
    quota_info = provider._parse_quota_error(mock_error)

    print("📊 Parsed quota information (structured parsing):")
    if quota_info:
        print(f"   ✅ Is quota error: {quota_info['is_quota_error']}")
        print(f"   ⏱️  Retry delay: {quota_info['retry_delay']} seconds")
        print(f"   📈 Quota metric: {quota_info['quota_metric']}")
        print(f"   🆔 Quota ID: {quota_info['quota_id']}")
        print(f"   📊 Quota value: {quota_info['quota_value']}")
        print(f"   🤖 Model: {quota_info['model']}")

        # Verify expected values
        expected_values = {
            'retry_delay': 46,
            'quota_metric': 'generativelanguage.googleapis.com/generate_content_free_tier_requests',
            'quota_id': 'GenerateRequestsPerMinutePerProjectPerModel-FreeTier',
            'quota_value': 15,
            'model': 'gemini-2.0-flash'
        }

        all_correct = True
        for key, expected in expected_values.items():
            if quota_info.get(key) != expected:
                print(
                    f"   ❌ {key}: Expected {expected}, got {quota_info.get(key)}")
                all_correct = False

        if all_correct:
            print("   ✅ All parsed values are correct!")
            print("   🎉 Structured parsing works without regex!")
            return True
        else:
            print("   ❌ Some parsed values are incorrect!")
            return False
    else:
        print("   ❌ Failed to parse quota error!")
        return False


def test_same_sample_retry_logic():
    """Test that the same loan sample is retried on quota errors."""
    print("\n🧪 Testing Same-Sample Retry Logic")
    print("=" * 60)

    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )

    # Create a mock provider
    provider = Mock(spec=GoogleProvider)
    provider.model_name = "gemini-2.0-flash"
    provider.config = config

    # Track which loan data is being processed
    processed_loan_data = []
    call_count = 0

    def mock_generate(prompt):
        nonlocal call_count
        call_count += 1

        # Extract loan amount from prompt to verify same loan is being retried
        if "loan_amnt: 25000.0" in prompt:
            processed_loan_data.append("loan_25000")
        elif "loan_amnt: 10000.0" in prompt:
            processed_loan_data.append("loan_10000")
        else:
            processed_loan_data.append("unknown_loan")

        if call_count <= 2:  # Fail first 2 attempts with quota error
            quota_info = {
                "is_quota_error": True,
                "retry_delay": 2,  # Short delay for testing
                "quota_metric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
                "quota_value": 15,
                "model": "gemini-2.0-flash"
            }
            raise QuotaExceededException(
                "Google API quota exceeded for model 'gemini-2.0-flash'",
                retry_delay=2,
                quota_info=quota_info
            )
        else:  # Succeed on 3rd attempt
            return {
                "success": True,
                "response": '{"decision": "APPROVE", "confidence": "MEDIUM", "risk_assessment": "LOW", "reasoning": "Test response", "key_factors": ["Test"], "positive_factors": ["Test"], "negative_factors": []}'
            }

    provider.generate = mock_generate

    # Create the approach
    approach = SingleFrontierApproach(provider)

    # Create test loan data with specific amount to track
    loan_data = LoanData(
        loan_amnt=25000.0,  # Specific amount to track in prompts
        int_rate=12.5,
        grade="B",
        sub_grade="B2",
        annual_inc=50000.0,
        dti=15.0,
        fico_range_low=700,
        fico_range_high=720,
        home_ownership=HomeOwnership.RENT,
        verification_status=VerificationStatus.VERIFIED,
        purpose=LoanPurpose.DEBT_CONSOLIDATION
    )

    print("🔄 Testing that same loan sample is retried...")
    print(f"   📋 Test loan amount: ${loan_data.loan_amnt}")

    start_time = time.time()

    # Test the prediction with max_retries=3
    result = approach.predict_single(loan_data, max_retries=3)

    end_time = time.time()
    total_time = end_time - start_time

    print(
        f"✅ Result received after {call_count} attempts in {total_time:.1f}s:")
    print(f"   Decision: {result.decision.value}")
    print(f"   Confidence: {result.confidence.value}")
    print(f"   Processed loan data: {processed_loan_data}")

    # Verify that the same loan was processed in all attempts
    if len(processed_loan_data) == 3 and all(loan == "loan_25000" for loan in processed_loan_data):
        print("   ✅ PASS: Same loan sample was retried consistently!")
        print("   🎯 This ensures accurate benchmarking data!")
        return True
    else:
        print(
            f"   ❌ FAIL: Expected 3 attempts with 'loan_25000', got {processed_loan_data}")
        return False


def test_quota_error_vs_deny_decision():
    """Test that quota errors don't result in DENY decisions."""
    print("\n🧪 Testing Quota Error vs DENY Decision")
    print("=" * 60)

    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )

    # Create a mock provider that always fails with quota error
    provider = Mock(spec=GoogleProvider)
    provider.model_name = "gemini-2.0-flash"
    provider.config = config

    def mock_generate_always_quota_error(prompt):
        quota_info = {
            "is_quota_error": True,
            "retry_delay": 1,
            "quota_metric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quota_value": 15,
            "model": "gemini-2.0-flash"
        }
        raise QuotaExceededException(
            "Google API quota exceeded for model 'gemini-2.0-flash'",
            retry_delay=1,
            quota_info=quota_info
        )

    provider.generate = mock_generate_always_quota_error

    # Create the approach
    approach = SingleFrontierApproach(provider)

    # Create test loan data
    loan_data = LoanData(
        loan_amnt=15000.0,
        int_rate=10.5,
        grade="A",
        sub_grade="A1",
        annual_inc=75000.0,
        dti=10.0,
        fico_range_low=750,
        fico_range_high=800,
        home_ownership=HomeOwnership.OWN,
        verification_status=VerificationStatus.VERIFIED,
        purpose=LoanPurpose.HOME_IMPROVEMENT
    )

    print("🔄 Testing quota error handling (max_retries=2 for speed)...")

    # Test with low max_retries for speed
    result = approach.predict_single(loan_data, max_retries=2)

    print(f"✅ Result after quota errors:")
    print(f"   Decision: {result.decision.value}")
    print(f"   Confidence: {result.confidence.value}")
    print(f"   Risk Assessment: {result.risk_assessment.value}")
    print(f"   Reasoning: {result.reasoning[:100]}...")

    # Verify the result is CONDITIONAL, not DENY
    if result.decision == LoanDecision.CONDITIONAL:
        print("   ✅ PASS: Quota error correctly results in CONDITIONAL (manual review)")
        print("   🎯 This prevents corrupting benchmarking data with false DENY decisions!")
        return True
    else:
        print(f"   ❌ FAIL: Expected CONDITIONAL, got {result.decision.value}")
        return False


if __name__ == "__main__":
    print("🏦 Structured Quota Parsing & Same-Sample Retry Test")
    print("=" * 70)

    tests_passed = 0
    total_tests = 3

    # Test 1: Structured quota parsing
    if test_structured_quota_parsing():
        tests_passed += 1

    # Test 2: Same sample retry logic
    if test_same_sample_retry_logic():
        tests_passed += 1

    # Test 3: Quota error vs DENY decision
    if test_quota_error_vs_deny_decision():
        tests_passed += 1

    print("\n" + "=" * 70)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("✅ All tests passed!")
        print("\n🎉 Key Improvements Verified:")
        print("   1. ✅ Structured parsing without regex")
        print("   2. ✅ Same loan sample retried consistently")
        print("   3. ✅ Quota errors result in CONDITIONAL, not DENY")
        print("   4. ✅ Accurate benchmarking data preservation")
    else:
        print("❌ Some tests failed!")
