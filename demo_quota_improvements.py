#!/usr/bin/env python3
"""
Demonstration script showing the quota handling improvements.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.providers.base import LLMConfig
from src.providers.google_provider import <PERSON><PERSON>rovider


def demonstrate_error_analysis():
    """Demonstrate how the improved system analyzes quota errors."""
    print("🔍 Quota Error Analysis Demonstration")
    print("=" * 60)
    
    # Your original error message
    original_error = """Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]."""
    
    # Create provider
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )
    provider = GoogleProvider(config)
    
    # Parse the error
    mock_error = Exception(original_error)
    quota_info = provider._parse_quota_error(mock_error)
    
    print("📊 EXTRACTED INFORMATION:")
    print("-" * 40)
    
    if quota_info:
        print(f"✅ Error Type: HTTP {quota_info['error_code']} ResourceExhausted")
        print(f"⏱️  API Retry Delay: {quota_info['retry_delay']} seconds")
        print(f"🤖 Model: {quota_info['model']}")
        print(f"📈 Quota Limit: {quota_info['quota_value']} requests/minute")
        print(f"🆔 Quota Type: {quota_info['quota_id']}")
        print(f"📊 Quota Metric: {quota_info['quota_metric']}")
        
        print("\n🧠 INTELLIGENT ANALYSIS:")
        print("-" * 40)
        
        # Analyze the quota type
        if "FreeTier" in quota_info['quota_id']:
            print("💡 You're on the FREE TIER with limited requests")
            print("   Consider upgrading for higher limits")
        
        # Analyze the retry delay
        api_delay = quota_info['retry_delay']
        recommended_delay = api_delay + (api_delay * 0.1) + 1
        print(f"⏰ WAIT TIME CALCULATION:")
        print(f"   • API suggests: {api_delay} seconds")
        print(f"   • With 10% buffer: {api_delay * 0.1:.1f} seconds")
        print(f"   • Safety margin: +1 second")
        print(f"   • TOTAL WAIT: {recommended_delay:.1f} seconds")
        
        # Determine if it's rate limiting vs token limits
        if "requests" in quota_info['quota_metric'].lower():
            print("🚦 ISSUE TYPE: Rate limiting (too many requests)")
            print("   Solution: Wait for the specified delay")
        elif "tokens" in quota_info['quota_metric'].lower():
            print("🔤 ISSUE TYPE: Token limit exceeded")
            print("   Solution: Reduce prompt size or wait")
        
        print(f"\n📅 NEXT RETRY TIME:")
        print(f"   Wait {recommended_delay:.1f} seconds before next request")
        print(f"   This ensures compliance with API limits")
        
    else:
        print("❌ Could not parse quota information")


def show_comparison():
    """Show before vs after comparison."""
    print("\n" + "=" * 60)
    print("📈 BEFORE vs AFTER COMPARISON")
    print("=" * 60)
    
    print("🔴 BEFORE (Old System):")
    print("   • Generic string matching for quota errors")
    print("   • Fixed exponential backoff: 5, 7, 9, 13 seconds")
    print("   • Limited error information")
    print("   • May retry too early, wasting API calls")
    print("   • Generic error messages")
    
    print("\n🟢 AFTER (Improved System):")
    print("   • Precise HTTP 429 + ResourceExhausted detection")
    print("   • API-suggested delays: 46s + buffer = 51.6s")
    print("   • Detailed quota information extraction")
    print("   • Optimal retry timing based on API guidance")
    print("   • Rich error context and debugging info")
    
    print("\n💡 KEY BENEFITS:")
    print("   ✅ Reduced API waste")
    print("   ✅ Faster recovery from quota errors")
    print("   ✅ Better user experience")
    print("   ✅ Enhanced debugging capabilities")
    print("   ✅ Intelligent wait time calculation")


def show_usage_tips():
    """Show usage tips and best practices."""
    print("\n" + "=" * 60)
    print("💡 USAGE TIPS & BEST PRACTICES")
    print("=" * 60)
    
    print("🎯 UNDERSTANDING YOUR QUOTA:")
    print("   • Free tier: 15 requests/minute for gemini-2.0-flash")
    print("   • Rate resets every minute")
    print("   • Monitor your usage patterns")
    
    print("\n⚡ OPTIMIZATION STRATEGIES:")
    print("   • Batch similar requests when possible")
    print("   • Use caching for repeated queries")
    print("   • Consider request timing distribution")
    print("   • Monitor quota usage in logs")
    
    print("\n🔧 WHEN TO UPGRADE:")
    print("   • Frequently hitting 15 requests/minute")
    print("   • Need higher throughput for production")
    print("   • Require more predictable performance")
    
    print("\n📊 MONITORING:")
    print("   • Watch for quota error patterns in logs")
    print("   • Track retry delays and success rates")
    print("   • Analyze peak usage times")


if __name__ == "__main__":
    print("🏦 Gemini API Quota Handling Improvements")
    print("🚀 Demonstration Script")
    
    demonstrate_error_analysis()
    show_comparison()
    show_usage_tips()
    
    print("\n" + "=" * 60)
    print("✅ DEMONSTRATION COMPLETE")
    print("=" * 60)
    print("Your system now has intelligent quota handling!")
    print("Run your loan approval predictions with confidence.")
