#!/usr/bin/env python3
"""
Debug script to test quota error parsing.
"""

from src.providers.google_provider import GoogleProvider
from src.providers.base import LLMConfig
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def debug_parsing():
    """Debug the parsing logic."""
    print("🔍 Debug Quota Error Parsing")
    print("=" * 50)

    # Create a mock config
    config = LLMConfig(
        provider="google",
        model_name="gemini-2.0-flash",
        api_key="test-key",
        temperature=0.7,
        max_tokens=1000
    )

    # Create the provider
    provider = GoogleProvider(config)

    # Your original error message
    error_message = """ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]."""

    print("📝 Original error message:")
    print(error_message)

    # Find violations section
    violations_start = error_message.find("[violations {")
    violations_end = error_message.find("}]", violations_start)

    print(f"\n🔍 Violations section positions:")
    print(f"   Start: {violations_start}")
    print(f"   End: {violations_end}")

    if violations_start != -1 and violations_end != -1:
        violations_block = error_message[violations_start:violations_end + 2]
        print(f"\n📋 Extracted violations block:")
        print(violations_block)

        # Test individual parsing
        print(f"\n🧪 Testing individual field extraction:")

        # Test quota_metric
        if 'quota_metric:' in violations_block:
            start_pos = violations_block.find('quota_metric:')
            quote_start = violations_block.find('"', start_pos)
            quote_end = violations_block.find('"', quote_start + 1)
            if quote_start != -1 and quote_end != -1:
                quota_metric = violations_block[quote_start + 1:quote_end]
                print(f"   ✅ quota_metric: {quota_metric}")
            else:
                print(f"   ❌ quota_metric: Could not find quotes")

        # Test quota_id
        if 'quota_id:' in violations_block:
            start_pos = violations_block.find('quota_id:')
            quote_start = violations_block.find('"', start_pos)
            quote_end = violations_block.find('"', quote_start + 1)
            if quote_start != -1 and quote_end != -1:
                quota_id = violations_block[quote_start + 1:quote_end]
                print(f"   ✅ quota_id: {quota_id}")
            else:
                print(f"   ❌ quota_id: Could not find quotes")

        # Test quota_value
        if 'quota_value:' in violations_block:
            start_pos = violations_block.find('quota_value:')
            line_end = violations_block.find('\n', start_pos)
            if line_end == -1:
                line_end = len(violations_block)
            value_line = violations_block[start_pos:line_end]
            colon_pos = value_line.find(':')
            if colon_pos != -1:
                value_str = value_line[colon_pos + 1:].strip()
                print(f"   ✅ quota_value: {value_str}")
            else:
                print(f"   ❌ quota_value: Could not find colon")

        # Test model
        if 'key: "model"' in violations_block:
            model_key_pos = violations_block.find('key: "model"')
            value_pos = violations_block.find('value:', model_key_pos)
            if value_pos != -1:
                quote_start = violations_block.find('"', value_pos)
                quote_end = violations_block.find('"', quote_start + 1)
                if quote_start != -1 and quote_end != -1:
                    model = violations_block[quote_start + 1:quote_end]
                    print(f"   ✅ model: {model}")
                else:
                    print(f"   ❌ model: Could not find quotes after value:")
            else:
                print(f"   ❌ model: Could not find value: after key")

    # Test the actual parsing method
    print(f"\n🧪 Testing actual parsing method:")
    mock_error = Exception(error_message)
    quota_info = provider._parse_quota_error(mock_error)

    if quota_info:
        print(f"   ✅ Parsed successfully:")
        for key, value in quota_info.items():
            print(f"      {key}: {value}")
    else:
        print(f"   ❌ Parsing failed")


if __name__ == "__main__":
    debug_parsing()
