"""
Google Gemini provider implementation.
"""

import os
from typing import Any, Dict
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

from .base import LLMProvider, LLMConfig


class QuotaExceededException(Exception):
    """Exception raised when API quota is exceeded."""
    pass


class GoogleProvider(LLMProvider):
    """Google Gemini provider."""

    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Google API key not found. Set GOOGLE_API_KEY environment variable.")

    def _initialize_client(self) -> ChatGoogleGenerativeAI:
        """Initialize the Google Gemini client."""
        return ChatGoogleGenerativeAI(
            model=self.model_name,
            temperature=self.config.temperature,
            max_output_tokens=self.config.max_tokens,
            google_api_key=self.api_key,
            **self.config.additional_params
        )

    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from Gemini."""
        client = self.get_client()

        # Override config parameters with kwargs if provided
        temp_params = {}
        if "temperature" in kwargs:
            temp_params["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            temp_params["max_output_tokens"] = kwargs["max_tokens"]

        try:
            # Create a temporary client with updated parameters if needed
            if temp_params:
                temp_client = ChatGoogleGenerativeAI(
                    model=self.model_name,
                    temperature=temp_params.get(
                        "temperature", self.config.temperature),
                    max_output_tokens=temp_params.get(
                        "max_output_tokens", self.config.max_tokens),
                    google_api_key=self.api_key,
                    **self.config.additional_params
                )
                response = temp_client.invoke([HumanMessage(content=prompt)])
            else:
                response = client.invoke([HumanMessage(content=prompt)])

            return response.content

        except Exception as e:
            error_msg = str(e).lower()
            # Check for quota-related errors
            if any(keyword in error_msg for keyword in ['quota', 'rate limit', 'too many requests', 'resource exhausted']):
                raise QuotaExceededException(
                    f"Google API quota exceeded: {str(e)}")
            else:
                # Re-raise other exceptions as-is
                raise e

    def validate_config(self) -> bool:
        """Validate Google configuration."""
        if not self.api_key:
            return False

        # Check if model name is valid for Google
        valid_models = [
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-1.0-pro",
            "gemini-pro",
            "gemini-pro-vision"
        ]

        return any(self.model_name.startswith(model) for model in valid_models)

    def get_model_info(self) -> Dict[str, Any]:
        """Get Google model information."""
        info = super().get_model_info()
        info.update({
            "api_key_configured": bool(self.api_key),
            "supported_features": ["text_generation", "conversation", "vision", "multimodal"]
        })
        return info
