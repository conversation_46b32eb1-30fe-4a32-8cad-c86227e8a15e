"""
Google Gemini provider implementation.
"""

import os
import re
import json
from typing import Any, Dict, Optional
from langchain_google_genai import ChatG<PERSON>gleGenerativeA<PERSON>
from langchain_core.messages import HumanMessage

from .base import <PERSON><PERSON><PERSON><PERSON>, LLMConfig


class QuotaExceededException(Exception):
    """Exception raised when API quota is exceeded."""

    def __init__(self, message: str, retry_delay: Optional[int] = None, quota_info: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.retry_delay = retry_delay
        self.quota_info = quota_info or {}


class GoogleProvider(LLMProvider):
    """Google Gemini provider."""

    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Google API key not found. Set GOOGLE_API_KEY environment variable.")

    def _initialize_client(self) -> ChatGoogleGenerativeAI:
        """Initialize the Google Gemini client."""
        return ChatGoogleGenerativeAI(
            model=self.model_name,
            temperature=self.config.temperature,
            max_output_tokens=self.config.max_tokens,
            google_api_key=self.api_key,
            **self.config.additional_params
        )

    def _parse_quota_error(self, error: Exception) -> Optional[Dict[str, Any]]:
        """
        Parse quota error information from Google API error.

        Args:
            error: The exception raised by the API

        Returns:
            Dictionary with quota error information or None if not a quota error
        """
        error_str = str(error)

        # Check if this is a ResourceExhausted error (HTTP 429)
        if "ResourceExhausted: 429" not in error_str:
            return None

        quota_info = {
            "is_quota_error": True,
            "error_code": 429,
            "retry_delay": None,
            "quota_metric": None,
            "quota_id": None,
            "quota_value": None,
            "model": None
        }

        # Extract retry delay
        retry_delay_match = re.search(
            r'retry_delay\s*{\s*seconds:\s*(\d+)', error_str)
        if retry_delay_match:
            quota_info["retry_delay"] = int(retry_delay_match.group(1))

        # Extract quota metric
        quota_metric_match = re.search(r'quota_metric:\s*"([^"]+)"', error_str)
        if quota_metric_match:
            quota_info["quota_metric"] = quota_metric_match.group(1)

        # Extract quota ID
        quota_id_match = re.search(r'quota_id:\s*"([^"]+)"', error_str)
        if quota_id_match:
            quota_info["quota_id"] = quota_id_match.group(1)

        # Extract quota value
        quota_value_match = re.search(r'quota_value:\s*(\d+)', error_str)
        if quota_value_match:
            quota_info["quota_value"] = int(quota_value_match.group(1))

        # Extract model from quota dimensions
        model_match = re.search(
            r'key:\s*"model"\s*value:\s*"([^"]+)"', error_str)
        if model_match:
            quota_info["model"] = model_match.group(1)

        return quota_info

    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from Gemini."""
        client = self.get_client()

        # Override config parameters with kwargs if provided
        temp_params = {}
        if "temperature" in kwargs:
            temp_params["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            temp_params["max_output_tokens"] = kwargs["max_tokens"]

        try:
            # Create a temporary client with updated parameters if needed
            if temp_params:
                temp_client = ChatGoogleGenerativeAI(
                    model=self.model_name,
                    temperature=temp_params.get(
                        "temperature", self.config.temperature),
                    max_output_tokens=temp_params.get(
                        "max_output_tokens", self.config.max_tokens),
                    google_api_key=self.api_key,
                    **self.config.additional_params
                )
                response = temp_client.invoke([HumanMessage(content=prompt)])
            else:
                response = client.invoke([HumanMessage(content=prompt)])

            return response.content

        except Exception as e:
            # Try to parse quota error information
            quota_info = self._parse_quota_error(e)

            if quota_info:
                # This is a quota error - extract retry delay and other info
                retry_delay = quota_info.get("retry_delay")
                quota_metric = quota_info.get("quota_metric", "unknown")
                quota_value = quota_info.get("quota_value", "unknown")
                model = quota_info.get("model", self.model_name)

                error_message = (
                    f"Google API quota exceeded for model '{model}'. "
                    f"Quota: {quota_metric} (limit: {quota_value} requests/minute). "
                    f"Retry delay: {retry_delay}s" if retry_delay else "No retry delay specified"
                )

                raise QuotaExceededException(
                    error_message,
                    retry_delay=retry_delay,
                    quota_info=quota_info
                )
            else:
                # Check for other quota-related keywords as fallback
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['quota', 'rate limit', 'too many requests', 'resource exhausted']):
                    raise QuotaExceededException(
                        f"Google API quota exceeded (fallback detection): {str(e)}")
                else:
                    # Re-raise other exceptions as-is
                    raise e

    def validate_config(self) -> bool:
        """Validate Google configuration."""
        if not self.api_key:
            return False

        # Check if model name is valid for Google
        valid_models = [
            "gemini-2.0-flash",
            "gemini-2.0-flash-lite",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-1.0-pro",
            "gemini-pro",
            "gemini-pro-vision"
        ]

        return any(self.model_name.startswith(model) for model in valid_models)

    def get_model_info(self) -> Dict[str, Any]:
        """Get Google model information."""
        info = super().get_model_info()
        info.update({
            "api_key_configured": bool(self.api_key),
            "supported_features": ["text_generation", "conversation", "vision", "multimodal"]
        })
        return info
