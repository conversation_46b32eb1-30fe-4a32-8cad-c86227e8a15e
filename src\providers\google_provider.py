"""
Google Gemini provider implementation.
"""

import os
from typing import Any, Dict, Optional
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from langchain_core.messages import HumanMessage

from .base import <PERSON><PERSON><PERSON><PERSON>, LLMConfig


class QuotaExceededException(Exception):
    """Exception raised when API quota is exceeded."""

    def __init__(self, message: str, retry_delay: Optional[int] = None, quota_info: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.retry_delay = retry_delay
        self.quota_info = quota_info or {}


class GoogleProvider(LLMProvider):
    """Google Gemini provider."""

    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_key = config.api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Google API key not found. Set GOOGLE_API_KEY environment variable.")

    def _initialize_client(self) -> ChatGoogleGenerativeAI:
        """Initialize the Google Gemini client."""
        return ChatGoogleGenerativeAI(
            model=self.model_name,
            temperature=self.config.temperature,
            max_output_tokens=self.config.max_tokens,
            google_api_key=self.api_key,
            **self.config.additional_params
        )

    def _parse_quota_error(self, error: Exception) -> Optional[Dict[str, Any]]:
        """
        Parse quota error information from Google API error.

        Args:
            error: The exception raised by the API

        Returns:
            Dictionary with quota error information or None if not a quota error
        """
        error_str = str(error)

        # Check if this is a ResourceExhausted error (HTTP 429)
        if "ResourceExhausted: 429" not in error_str:
            return None

        quota_info = {
            "is_quota_error": True,
            "error_code": 429,
            "retry_delay": None,
            "quota_metric": None,
            "quota_id": None,
            "quota_value": None,
            "model": None,
            "violations": []
        }

        try:
            # Parse the structured error information
            # The error contains structured data in a protobuf-like format
            # Let's extract it as a structured object

            # Find the violations section
            violations_start = error_str.find("[violations {")
            if violations_start != -1:
                # Find the end of the violations block - look for the closing } followed by comma or ]
                # The structure is: [violations { ... } , links { ... } , retry_delay { ... } ]
                violations_content_start = violations_start + \
                    len("[violations {")

                # Find the matching closing brace for the violations block
                brace_count = 1
                pos = violations_content_start
                violations_end = -1

                while pos < len(error_str) and brace_count > 0:
                    if error_str[pos] == '{':
                        brace_count += 1
                    elif error_str[pos] == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            violations_end = pos
                            break
                    pos += 1

                if violations_end != -1:
                    violations_block = error_str[violations_start:violations_end + 1]
                    parsed_violation = self._parse_violation_block(
                        violations_block)
                    if parsed_violation:
                        quota_info.update(parsed_violation)

            # Find retry_delay section
            retry_delay_start = error_str.find("retry_delay {")
            if retry_delay_start != -1:
                retry_delay_end = error_str.find("}", retry_delay_start)
                if retry_delay_end != -1:
                    retry_block = error_str[retry_delay_start:retry_delay_end + 1]
                    retry_info = self._parse_retry_delay_block(retry_block)
                    if retry_info:
                        quota_info["retry_delay"] = retry_info["seconds"]

        except Exception as parse_error:
            # If structured parsing fails, fall back to basic detection
            print(
                f"Warning: Failed to parse quota error structure: {parse_error}")
            quota_info["parsing_error"] = str(parse_error)

        return quota_info

    def _parse_violation_block(self, violations_block: str) -> Optional[Dict[str, Any]]:
        """Parse the violations block from the error message."""
        violation_info = {}

        try:
            # More robust parsing approach
            # Look for each field individually in the entire block

            # Extract quota_metric
            if 'quota_metric:' in violations_block:
                start_pos = violations_block.find('quota_metric:')
                if start_pos != -1:
                    # Find the quoted value after quota_metric:
                    quote_start = violations_block.find('"', start_pos)
                    if quote_start != -1:
                        quote_end = violations_block.find('"', quote_start + 1)
                        if quote_end != -1:
                            violation_info["quota_metric"] = violations_block[quote_start + 1:quote_end]

            # Extract quota_id
            if 'quota_id:' in violations_block:
                start_pos = violations_block.find('quota_id:')
                if start_pos != -1:
                    quote_start = violations_block.find('"', start_pos)
                    if quote_start != -1:
                        quote_end = violations_block.find('"', quote_start + 1)
                        if quote_end != -1:
                            violation_info["quota_id"] = violations_block[quote_start + 1:quote_end]

            # Extract quota_value
            if 'quota_value:' in violations_block:
                start_pos = violations_block.find('quota_value:')
                if start_pos != -1:
                    # Find the number after quota_value:
                    line_end = violations_block.find('\n', start_pos)
                    if line_end == -1:
                        line_end = len(violations_block)
                    value_line = violations_block[start_pos:line_end]
                    # Extract number
                    colon_pos = value_line.find(':')
                    if colon_pos != -1:
                        value_str = value_line[colon_pos + 1:].strip()
                        try:
                            violation_info["quota_value"] = int(value_str)
                        except ValueError:
                            pass

            # Extract model from quota_dimensions
            if 'key: "model"' in violations_block:
                # Find the model key section
                model_key_pos = violations_block.find('key: "model"')
                if model_key_pos != -1:
                    # Look for the value after this key
                    value_pos = violations_block.find('value:', model_key_pos)
                    if value_pos != -1:
                        quote_start = violations_block.find('"', value_pos)
                        if quote_start != -1:
                            quote_end = violations_block.find(
                                '"', quote_start + 1)
                            if quote_end != -1:
                                violation_info["model"] = violations_block[quote_start + 1:quote_end]

        except Exception as e:
            print(f"Warning: Error parsing violation block: {e}")
            return None

        return violation_info

    def _parse_retry_delay_block(self, retry_block: str) -> Optional[Dict[str, Any]]:
        """Parse the retry_delay block from the error message."""
        try:
            # Look for seconds: value
            if 'seconds:' in retry_block:
                parts = retry_block.split('seconds:')
                if len(parts) > 1:
                    # Extract the number
                    seconds_part = parts[1].strip().rstrip('}').strip()
                    try:
                        return {"seconds": int(seconds_part)}
                    except ValueError:
                        pass
        except Exception as e:
            print(f"Warning: Error parsing retry delay block: {e}")

        return None

    def _generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response from Gemini."""
        client = self.get_client()

        # Override config parameters with kwargs if provided
        temp_params = {}
        if "temperature" in kwargs:
            temp_params["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            temp_params["max_output_tokens"] = kwargs["max_tokens"]

        try:
            # Create a temporary client with updated parameters if needed
            if temp_params:
                temp_client = ChatGoogleGenerativeAI(
                    model=self.model_name,
                    temperature=temp_params.get(
                        "temperature", self.config.temperature),
                    max_output_tokens=temp_params.get(
                        "max_output_tokens", self.config.max_tokens),
                    google_api_key=self.api_key,
                    **self.config.additional_params
                )
                response = temp_client.invoke([HumanMessage(content=prompt)])
            else:
                response = client.invoke([HumanMessage(content=prompt)])

            return response.content

        except Exception as e:
            # Try to parse quota error information
            quota_info = self._parse_quota_error(e)

            # Debug: Print what we extracted
            print(f"DEBUG: Full error message: {str(e)}")
            print(f"DEBUG: Error type: {type(e).__name__}")
            if quota_info:
                print(
                    f"DEBUG: Quota info extracted: retry_delay={quota_info.get('retry_delay')}")
            else:
                print(f"DEBUG: No quota info extracted")

            if quota_info:
                # This is a quota error - extract retry delay and other info
                retry_delay = quota_info.get("retry_delay")
                quota_metric = quota_info.get("quota_metric", "unknown")
                quota_value = quota_info.get("quota_value", "unknown")
                model = quota_info.get("model", self.model_name)

                error_message = (
                    f"Google API quota exceeded for model '{model}'. "
                    f"Quota: {quota_metric} (limit: {quota_value} requests/minute). "
                    f"Retry delay: {retry_delay}s" if retry_delay else "No retry delay specified"
                )

                print(
                    f"DEBUG: Creating QuotaExceededException with retry_delay={retry_delay}")
                raise QuotaExceededException(
                    error_message,
                    retry_delay=retry_delay,
                    quota_info=quota_info
                )
            else:
                # Check for other quota-related keywords as fallback
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['quota', 'rate limit', 'too many requests', 'resource exhausted']):
                    raise QuotaExceededException(
                        f"Google API quota exceeded (fallback detection): {str(e)}")
                else:
                    # Re-raise other exceptions as-is
                    raise e

    def validate_config(self) -> bool:
        """Validate Google configuration."""
        if not self.api_key:
            return False

        # Check if model name is valid for Google
        valid_models = [
            "gemini-2.0-flash",
            "gemini-2.0-flash-lite",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-1.0-pro",
            "gemini-pro",
            "gemini-pro-vision"
        ]

        return any(self.model_name.startswith(model) for model in valid_models)

    def get_model_info(self) -> Dict[str, Any]:
        """Get Google model information."""
        info = super().get_model_info()
        info.update({
            "api_key_configured": bool(self.api_key),
            "supported_features": ["text_generation", "conversation", "vision", "multimodal"]
        })
        return info
