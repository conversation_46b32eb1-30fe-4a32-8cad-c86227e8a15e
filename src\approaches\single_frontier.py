"""
Single Frontier Model approach for loan approval prediction.
"""

import re
import json
import time
import random
from typing import Dict, Any, Optional
from datetime import datetime

from .base import LoanApprovalApproach, PromptTemplate
from ..models.loan_data import LoanData
from ..models.llm_response import SingleModelResponse, LoanDecision, ConfidenceLevel, RiskLevel
from ..providers.base import LLMProvider
from ..providers.google_provider import QuotaExceededException


class SingleFrontierApproach(LoanApprovalApproach):
    """
    Single Frontier Model approach using one high-quality LLM for loan approval decisions.

    This approach uses a single, powerful language model (like GPT-4, Claude, or Gemini)
    to evaluate loan applications and make approval/denial decisions.
    """

    def __init__(self, provider: LLMProvider, name: str = "Single Frontier Model"):
        super().__init__(name)
        self.provider = provider
        self.prompt_template = PromptTemplate()

    def predict_single(self, loan_data: LoanData) -> SingleModelResponse:
        """
        Predict loan approval using a single frontier model.

        Args:
            loan_data: Loan application data

        Returns:
            SingleModelResponse with prediction and reasoning
        """
        start_time = time.time()

        try:
            # Generate the evaluation prompt
            prompt = self._create_evaluation_prompt(loan_data)

            # Get response from the LLM with retry logic for quota errors
            llm_response = self._generate_with_retry(prompt)

            if not llm_response.get("success", False):
                raise Exception(
                    f"LLM generation failed: {llm_response.get('error', 'Unknown error')}")

            # Parse the response
            parsed_response = self._parse_llm_response(
                llm_response["response"])

            # Apply decision mapping for CONDITIONAL responses
            final_decision = self._map_conditional_decision(parsed_response)
            parsed_response["decision"] = final_decision

            # Create structured response
            processing_time = time.time() - start_time

            response = SingleModelResponse(
                decision=parsed_response["decision"],
                confidence=parsed_response["confidence"],
                risk_assessment=parsed_response["risk_assessment"],
                reasoning=parsed_response["reasoning"],
                key_factors=parsed_response["key_factors"],
                positive_factors=parsed_response["positive_factors"],
                negative_factors=parsed_response["negative_factors"],
                approval_probability=parsed_response.get(
                    "approval_probability"),
                default_probability=parsed_response.get("default_probability"),
                model_name=self.provider.model_name,
                processing_time=processing_time,
                temperature_used=self.provider.config.temperature,
                max_tokens_used=self.provider.config.max_tokens
            )

            return response

        except QuotaExceededException as e:
            processing_time = time.time() - start_time

            # Return a special response for quota errors - don't default to DENY
            return SingleModelResponse(
                decision=LoanDecision.CONDITIONAL,  # Use CONDITIONAL instead of DENY
                confidence=ConfidenceLevel.VERY_LOW,
                risk_assessment=RiskLevel.MEDIUM,  # Use MEDIUM instead of VERY_HIGH
                reasoning=f"Unable to evaluate loan due to API quota limits. Manual review required: {str(e)}",
                key_factors=["API Quota Exceeded", "Manual Review Required"],
                negative_factors=[f"Quota error: {str(e)}"],
                model_name=self.provider.model_name,
                processing_time=processing_time
            )

        except Exception as e:
            processing_time = time.time() - start_time

            # Return error response for other errors
            return SingleModelResponse(
                decision=LoanDecision.DENY,
                confidence=ConfidenceLevel.VERY_LOW,
                risk_assessment=RiskLevel.VERY_HIGH,
                reasoning=f"Error in loan evaluation: {str(e)}",
                key_factors=["System Error"],
                negative_factors=[f"Processing error: {str(e)}"],
                model_name=self.provider.model_name,
                processing_time=processing_time
            )

    def _generate_with_retry(self, prompt: str, max_retries: int = 5) -> Dict[str, Any]:
        """
        Generate response with retry logic for quota errors.

        Args:
            prompt: The prompt to send to the LLM
            max_retries: Maximum number of retry attempts

        Returns:
            LLM response dictionary

        Raises:
            QuotaExceededException: If quota errors persist after all retries
        """
        for attempt in range(max_retries + 1):
            try:
                return self.provider.generate(prompt)
            except Exception as e:
                # Check if this is a quota error
                error_msg = str(e).lower()
                is_quota_error = any(keyword in error_msg for keyword in
                                     ['quota', 'rate limit', 'too many requests', 'resource exhausted'])

                if is_quota_error and attempt < max_retries:
                    # Calculate exponential backoff with jitter
                    base_delay = 5 + 2 ** attempt  # 1, 2, 4 seconds
                    jitter = random.uniform(0.1, 0.5)  # Add randomness
                    delay = base_delay + jitter

                    print(
                        f"⚠️  Quota error on attempt {attempt + 1}/{max_retries + 1}. Retrying in {delay:.1f}s...")
                    time.sleep(delay)
                    continue
                else:
                    # Re-raise the original exception
                    raise e

        # This should never be reached, but just in case
        raise QuotaExceededException("Max retries exceeded for quota errors")

    def _create_evaluation_prompt(self, loan_data: LoanData) -> str:
        """Create the evaluation prompt for the LLM."""
        system_prompt = self.prompt_template.SYSTEM_PROMPT
        evaluation_prompt = self.prompt_template.format_loan_evaluation(
            loan_data)

        return f"{system_prompt}\n\n{evaluation_prompt}"

    def _parse_llm_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse the LLM response into structured data using JSON parsing with fallback to regex.

        Args:
            response_text: Raw response from the LLM

        Returns:
            Dictionary with parsed response components
        """
        parsed = {
            "decision": LoanDecision.DENY,  # Default to deny for safety
            "confidence": ConfidenceLevel.LOW,
            "risk_assessment": RiskLevel.HIGH,
            "reasoning": "",
            "key_factors": [],
            "positive_factors": [],
            "negative_factors": [],
            "approval_probability": None,
            "default_probability": None
        }

        # First, try to parse as JSON
        try:
            # Extract JSON from response (handle cases where LLM adds extra text)
            json_data = self._extract_json_from_response(response_text)

            if json_data:
                # Parse decision
                if "decision" in json_data:
                    decision_str = str(json_data["decision"]).upper()
                    if decision_str in ["APPROVE", "APPROVED"]:
                        parsed["decision"] = LoanDecision.APPROVE
                    elif decision_str in ["DENY", "DENIED", "REJECT", "REJECTED"]:
                        parsed["decision"] = LoanDecision.DENY
                    elif decision_str in ["CONDITIONAL"]:
                        parsed["decision"] = LoanDecision.CONDITIONAL

                # Parse confidence
                if "confidence" in json_data:
                    confidence_str = str(
                        json_data["confidence"]).upper().replace(" ", "_")
                    try:
                        parsed["confidence"] = ConfidenceLevel(confidence_str)
                    except ValueError:
                        pass  # Keep default

                # Parse risk assessment
                if "risk_assessment" in json_data:
                    risk_str = str(
                        json_data["risk_assessment"]).upper().replace(" ", "_")
                    try:
                        parsed["risk_assessment"] = RiskLevel(risk_str)
                    except ValueError:
                        pass  # Keep default

                # Parse text fields
                if "reasoning" in json_data:
                    parsed["reasoning"] = str(json_data["reasoning"])

                # Parse list fields
                if "key_factors" in json_data and isinstance(json_data["key_factors"], list):
                    parsed["key_factors"] = [
                        str(item) for item in json_data["key_factors"][:10]]

                if "positive_factors" in json_data and isinstance(json_data["positive_factors"], list):
                    parsed["positive_factors"] = [
                        str(item) for item in json_data["positive_factors"][:10]]

                if "negative_factors" in json_data and isinstance(json_data["negative_factors"], list):
                    parsed["negative_factors"] = [
                        str(item) for item in json_data["negative_factors"][:10]]

                # Parse probabilities
                if "approval_probability" in json_data:
                    try:
                        parsed["approval_probability"] = float(
                            json_data["approval_probability"])
                    except (ValueError, TypeError):
                        pass

                if "default_probability" in json_data:
                    try:
                        parsed["default_probability"] = float(
                            json_data["default_probability"])
                    except (ValueError, TypeError):
                        pass

                return parsed

        except Exception as e:
            # If JSON parsing fails, fall back to regex parsing
            print(f"JSON parsing failed, falling back to regex: {str(e)}")
            return self._parse_llm_response_regex(response_text)

        # If no JSON found, fall back to regex parsing
        return self._parse_llm_response_regex(response_text)

    def _extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        Extract JSON object from LLM response text.

        Args:
            response_text: Raw response from the LLM

        Returns:
            Parsed JSON dictionary or None if no valid JSON found
        """
        # Try to find JSON in the response
        json_patterns = [
            # Look for JSON between curly braces
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',
            # Look for JSON after "json" keyword
            r'(?:json|JSON)\s*:?\s*(\{.*?\})',
            # Look for JSON in code blocks
            r'```(?:json)?\s*(\{.*?\})\s*```'
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, response_text,
                                 re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    # If the pattern captured a group, use that; otherwise use the full match
                    json_str = match if isinstance(
                        match, str) and match.startswith('{') else match
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    continue

        # Try parsing the entire response as JSON
        try:
            return json.loads(response_text.strip())
        except json.JSONDecodeError:
            pass

        return None

    def _parse_llm_response_regex(self, response_text: str) -> Dict[str, Any]:
        """
        Parse the LLM response into structured data using regex (fallback method).

        Args:
            response_text: Raw response from the LLM

        Returns:
            Dictionary with parsed response components
        """
        parsed = {
            "decision": LoanDecision.DENY,  # Default to deny for safety
            "confidence": ConfidenceLevel.LOW,
            "risk_assessment": RiskLevel.HIGH,
            "reasoning": "",
            "key_factors": [],
            "positive_factors": [],
            "negative_factors": [],
            "approval_probability": None,
            "default_probability": None
        }

        try:
            # Extract decision
            decision_match = re.search(
                r'(?:DECISION|Decision):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if decision_match:
                decision_str = decision_match.group(1).upper()
                if decision_str in ["APPROVE", "APPROVED"]:
                    parsed["decision"] = LoanDecision.APPROVE
                elif decision_str in ["DENY", "DENIED", "REJECT", "REJECTED"]:
                    parsed["decision"] = LoanDecision.DENY
                elif decision_str in ["CONDITIONAL"]:
                    parsed["decision"] = LoanDecision.CONDITIONAL

            # Extract confidence
            confidence_match = re.search(
                r'(?:CONFIDENCE|Confidence):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if confidence_match:
                confidence_str = confidence_match.group(
                    1).upper().replace(" ", "_")
                try:
                    parsed["confidence"] = ConfidenceLevel(confidence_str)
                except ValueError:
                    pass  # Keep default

            # Extract risk assessment
            risk_match = re.search(
                r'(?:RISK_ASSESSMENT|Risk Assessment):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if risk_match:
                risk_str = risk_match.group(1).upper().replace(" ", "_")
                try:
                    parsed["risk_assessment"] = RiskLevel(risk_str)
                except ValueError:
                    pass  # Keep default

            # Extract reasoning
            reasoning_match = re.search(
                r'(?:REASONING|Reasoning):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if reasoning_match:
                parsed["reasoning"] = reasoning_match.group(1).strip()
            else:
                # Fallback: use the entire response as reasoning
                parsed["reasoning"] = response_text.strip()

            # Extract key factors
            key_factors_match = re.search(
                r'(?:KEY_FACTORS|Key Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if key_factors_match:
                factors_text = key_factors_match.group(1)
                parsed["key_factors"] = self._extract_list_items(factors_text)

            # Extract positive factors
            positive_match = re.search(
                r'(?:POSITIVE_FACTORS|Positive Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if positive_match:
                positive_text = positive_match.group(1)
                parsed["positive_factors"] = self._extract_list_items(
                    positive_text)

            # Extract negative factors
            negative_match = re.search(
                r'(?:NEGATIVE_FACTORS|Negative Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if negative_match:
                negative_text = negative_match.group(1)
                parsed["negative_factors"] = self._extract_list_items(
                    negative_text)

            # Extract probabilities
            approval_prob_match = re.search(
                r'(?:APPROVAL_PROBABILITY|Approval Probability):\s*([0-9.]+)', response_text, re.IGNORECASE)
            if approval_prob_match:
                try:
                    parsed["approval_probability"] = float(
                        approval_prob_match.group(1))
                except ValueError:
                    pass

            default_prob_match = re.search(
                r'(?:DEFAULT_PROBABILITY|Default Probability):\s*([0-9.]+)', response_text, re.IGNORECASE)
            if default_prob_match:
                try:
                    parsed["default_probability"] = float(
                        default_prob_match.group(1))
                except ValueError:
                    pass

        except Exception as e:
            # If parsing fails, at least include the raw response in reasoning
            parsed["reasoning"] = f"Failed to parse response: {str(e)}\n\nRaw response:\n{response_text}"

        return parsed

    def _extract_list_items(self, text: str) -> list:
        """Extract list items from text (handles bullet points, numbers, etc.)."""
        items = []

        # Split by lines and clean up
        lines = text.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Remove common list markers
            line = re.sub(r'^[-*•]\s*', '', line)  # Bullet points
            line = re.sub(r'^\d+\.\s*', '', line)  # Numbered lists
            line = re.sub(r'^[a-zA-Z]\.\s*', '', line)  # Letter lists

            if line:
                items.append(line)

        # If no items found, try comma-separated
        if not items and ',' in text:
            items = [item.strip() for item in text.split(',') if item.strip()]

        return items[:10]  # Limit to 10 items

    def _map_conditional_decision(self, parsed_response: Dict[str, Any], threshold: float = 0.6) -> LoanDecision:
        """
        Map CONDITIONAL decisions to APPROVE/DENY based on approval probability.

        Args:
            parsed_response: Parsed LLM response
            threshold: Approval probability threshold (default: 0.6)

        Returns:
            Final decision (APPROVE or DENY)
        """
        decision = parsed_response.get("decision", LoanDecision.DENY)

        # If already APPROVE or DENY, keep as is
        if decision in [LoanDecision.APPROVE, LoanDecision.DENY]:
            return decision

        # For CONDITIONAL decisions, use approval probability if available
        if decision == LoanDecision.CONDITIONAL:
            approval_probability = parsed_response.get("approval_probability")

            if approval_probability is not None and approval_probability >= threshold:
                return LoanDecision.APPROVE

        # Default to DENY for safety
        return LoanDecision.DENY

    def get_approach_info(self) -> Dict[str, Any]:
        """Get information about this approach."""
        info = super().get_approach_info()
        info.update({
            "provider": self.provider.provider_name,
            "model_name": self.provider.model_name,
            "model_info": self.provider.get_model_info()
        })
        return info
